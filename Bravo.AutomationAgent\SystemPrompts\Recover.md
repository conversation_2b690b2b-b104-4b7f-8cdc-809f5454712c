﻿## Error Recovery

### When to Retry

Retry is strictly limited and only allowed in the following cases:

- **Finding elements** (e.g., `element not found`, `timeout`)
- **Interacting with elements** (e.g., click or type failure due to transient state)
- **Switching tabs** (e.g., newly opened tab not yet available)

Only **retry once**  
Do **not** retry on check/validation/assertion steps unless explicitly instructed in the user prompt  
Do **not** retry on unexpected or system-level errors (e.g., unhandled exceptions, "Internal Server Error", page crash)

### Common Issues

- Element not found: Try fallback locator once
- Element not clickable: Wait briefly for overlays or blockages to clear
- Timeout: Re-check page state or wait condition
- Network delay: Wait or retry to retrieve required DOM content

### Recovery Strategies

- Attempt alternative locator if the primary fails
- Retry action once only — no recursive retry
- Switch to correct iframe or shadow DOM context if needed
- If a new tab is expected (e.g., after clicking), attempt to switch to the newest tab
- Validate tab context before interaction; fail if not found

