namespace Bravo.AutomationAgent.Services.Configuration;

public class SystemPromptService(ILogger<SystemPromptService> logger) : ISystemPromptService
{
    private const string GetDefaultSystemPrompt = """
                                                  Simulate a manual tester with automation precision.
                                                  Follow test steps exactly as described, execute strictly in order, and report results in a structured format. Do not guess, assume, or optimize the intent.
                                                  """;

    private static readonly string[] SystemPromts =
    [
        "Core.md",
        "Libs.md",
        "Control.md",
        "Recover.md",
        "Validation.md",
        "ResponseFormat.md"
    ];

    private string? _cachedSystemPrompt;

    public async Task<string> GetSystemPromptAsync()
    {
        if (_cachedSystemPrompt != null)
            return _cachedSystemPrompt;

        try
        {
            var baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SystemPrompts");
            foreach (var prompt in SystemPromts)
            {
                _cachedSystemPrompt +=
                    await LoadPromptFileAsync(Path.Combine(baseDir, prompt), $"{prompt} system prompt");
                _cachedSystemPrompt += "\n\n"; // Add spacing between prompts
            }

            logger.LogInformation("System prompts loaded successfully");
            return _cachedSystemPrompt ?? string.Empty;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to load system prompts");
            return GetDefaultSystemPrompt;
        }
    }

    private async Task<string> LoadPromptFileAsync(string filePath, string description)
    {
        if (File.Exists(filePath)) return await File.ReadAllTextAsync(filePath);
        logger.LogWarning("{Description} file not found at: {Path}", description, filePath);
        return string.Empty;
    }
}