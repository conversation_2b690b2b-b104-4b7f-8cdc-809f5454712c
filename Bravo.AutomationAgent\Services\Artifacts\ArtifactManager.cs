using System.Reflection;
using Bravo.AutomationAgent.Models.Configuration;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace Bravo.AutomationAgent.Services.Artifacts;

public class ArtifactManager(
    IOptions<ArtifactSettings> artifactSettings,
    ILogger<ArtifactManager> logger) : IArtifactManager
{
    private readonly ArtifactSettings _artifactSettings = artifactSettings.Value;

    private string CreateTestDirectory(string testPath)
    {
        try
        {
            if (Directory.Exists(testPath))
            {
                Directory.Delete(testPath, true);
            }

            Directory.CreateDirectory(testPath);
            logger.LogInformation("Created test directory: {TestPath}", testPath);
            return testPath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create test directory: {TestPath}", testPath);
            throw;
        }
    }

    public async Task<string> SaveLogAsync(string artifactPath, string logContent)
    {
        var logPath = Path.Combine(artifactPath, "log.txt");

        try
        {
            await File.WriteAllTextAsync(logPath, logContent);
            logger.LogDebug("Saved log file: {LogPath}", logPath);
            return "log.txt";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save log file: {LogPath}", logPath);
            throw;
        }
    }

    public async Task<string> SaveRawResultAsync(string artifactPath, object rawResult)
    {
        var rawPath = Path.Combine(artifactPath, "result-raw.json");

        try
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(rawResult, options);
            await File.WriteAllTextAsync(rawPath, json);

            logger.LogDebug("Saved raw result: {RawPath}", rawPath);
            return "result-raw.json";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save raw result: {RawPath}", rawPath);
            throw;
        }
    }

    public string GetArtifactPath(string environment, string? testId = null)
    {
        var basePath = GetResultBasePath();
        var path = Path.Combine(basePath, environment, testId ?? string.Empty);
        return Directory.Exists(path) ? path : CreateTestDirectory(path);
    }

    private string GetResultBasePath()
    {
        var execLocation = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ??
                           AppDomain.CurrentDomain.BaseDirectory;
        var resultPath = _artifactSettings.ResultPath;

        return Path.IsPathRooted(resultPath) ? resultPath : Path.Combine(execLocation, resultPath);
    }
}