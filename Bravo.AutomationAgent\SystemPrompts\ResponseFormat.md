﻿## Response Format

The system must return a structured, machine-readable test result that is clean, sequential, and complete. It should be easy to consume by CI tools, downstream AI, or manual reviewers.

### Structure

- **Title**: Test case name
- **Status**: `Passed` or `Failed`
- **Start time**: Epoch timestamp in milliseconds
- **End time**:  Epoch timestamp in milliseconds
- **Trace or video**: if test case is failed
- **Steps**: Ordered list of all user-defined actions:
  - Step description: `"Action performed: <details>"`
  - Status: `Passed` or `Failed`
  - Start time: Epoch timestamp in milliseconds
  - End time: Epoch timestamp in milliseconds
  - Error message (if any)
  - Screenshot (full-page or element-level, if failed)

### Status Rules

- Overall `status` must be `"Failed"` if **any** step fails (soft or hard)
- Only return `"Passed"` if **all steps succeed without error**
