using Bravo.AutomationAgent.Models.Configuration;
using Microsoft.Extensions.Options;
using System.Text;

namespace Bravo.AutomationAgent.Services.Configuration;

public class EnvironmentService(
    IConfiguration configuration,
    ILogger<EnvironmentService> logger) : IEnvironmentService
{
    private readonly Dictionary<string, EnvironmentConfig> _environments = LoadEnvironments(configuration);

    public EnvironmentConfig? GetEnvironmentConfig(string environment)
    {
        return _environments.TryGetValue(environment.ToLowerInvariant(), out var config) ? config : null;
    }

    public string BuildEnvironmentContext(string environment)
    {
        var config = GetEnvironmentConfig(environment);
        if (config == null)
        {
            logger.LogWarning("Environment {Environment} not found, using default context", environment);
            return $"Environment: {environment} (configuration not available)";
        }

        var context = new StringBuilder();
        context.AppendLine($"Environment: {environment}");
        context.AppendLine($"Base URL: {config.BaseUrl}");
        
        if (config.Credentials.Any())
        {
            context.AppendLine("Available credentials:");
            foreach (var cred in config.Credentials)
            {
                context.AppendLine($"  - {cred.Key}: {cred.Value}");
            }
        }
        
        if (config.AdditionalSettings.Any())
        {
            context.AppendLine("Additional settings:");
            foreach (var setting in config.AdditionalSettings)
            {
                context.AppendLine($"  - {setting.Key}: {setting.Value}");
            }
        }

        return context.ToString();
    }

    public bool IsValidEnvironment(string environment)
    {
        return _environments.ContainsKey(environment.ToLowerInvariant());
    }

    public IEnumerable<string> GetAvailableEnvironments()
    {
        return _environments.Keys;
    }

    private static Dictionary<string, EnvironmentConfig> LoadEnvironments(IConfiguration configuration)
    {
        var environments = new Dictionary<string, EnvironmentConfig>();
        var environmentsSection = configuration.GetSection("Environments");
        
        foreach (var envSection in environmentsSection.GetChildren())
        {
            var config = new EnvironmentConfig();
            envSection.Bind(config);
            environments[envSection.Key.ToLowerInvariant()] = config;
        }

        return environments;
    }
}
