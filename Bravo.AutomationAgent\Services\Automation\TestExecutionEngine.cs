using System.Text;
using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Configuration;
using Microsoft.Extensions.AI;
using ModelContextProtocol.Client;

namespace Bravo.AutomationAgent.Services.Automation;

public class TestExecutionEngine(
    ISystemPromptService systemPromptService,
    IEnvironmentService environmentService,
    IArtifactManager artifactManager,
    IChatClientFactory chatClientFactory,
    ILogger<TestExecutionEngine> logger)
    : ITestExecutionEngine
{
    public async Task<ApiResponse> ExecuteTestAsync(TestExecutionRequest request, CancellationToken cancellationToken)
    {
        IChatClient? client = null;
        IMcpClient? mcpClient = null;

        try
        {
            // Initialize test environment and artifacts
            var artifactPath = artifactManager.GetArtifactPath(request.Environment, request.GetOrGenerateTestId());

            // Load system prompt and build enhanced user prompt
            var systemPrompt = await systemPromptService.GetSystemPromptAsync();
            var userPrompt = BuildEnhancedPrompt(request);

            logger.LogInformation("System prompt loaded, length: {Length} characters", systemPrompt.Length);
            logger.LogInformation("Enhanced user prompt built, length: {Length} characters", userPrompt.Length);

            // Execute test via MCP
            var executionStart = DateTime.UtcNow;
            client = chatClientFactory.CreateClient();
            mcpClient = await GetMcpClient(artifactPath, cancellationToken);
            var tools = await mcpClient.ListToolsAsync(cancellationToken: cancellationToken);

            logger.LogInformation("Mcp client created with: {TotalCount} tools", tools.Count);

            var messages = new List<ChatMessage>
            {
                new(ChatRole.System, systemPrompt),
                new(ChatRole.User, userPrompt)
            };

            var updates = new List<ChatResponseUpdate>();
            await foreach (var update in client.GetStreamingResponseAsync(
                               messages,
                               new ChatOptions
                               {
                                   Tools = [.. tools],
                                   Temperature = 0.1f
                               },
                               cancellationToken))
            {
                updates.Add(update);
                if (updates.Count % 10 == 0)
                {
                    logger.LogDebug("Received {UpdateCount} updates", updates.Count);
                }
            }

            var executionTime = DateTime.UtcNow - executionStart;

            logger.LogInformation("Test execution completed in {Duration}ms with {UpdateCount} updates",
                executionTime.TotalMilliseconds, updates.Count);


            // Prepare raw execution data
            var rawData = new RawExecutionData
            {
                McpResponse = updates,
            };
            await mcpClient.DisposeAsync();
            client.Dispose();
            return new ApiResponse
            {
                Raw = rawData,
                Result = updates.LastOrDefault()?.Text ?? "No response",
            };
        }
        catch (Exception ex)
        {
            mcpClient?.DisposeAsync();
            client?.Dispose();
            logger.LogError(ex, "Failed to execute test case: {Title}", request.Title);
            throw;
        }
    }

    private string BuildEnhancedPrompt(TestExecutionRequest request)
    {
        var environmentContext = environmentService.BuildEnvironmentContext(request.Environment);
        var prompt = $"""
                      # Test case:
                      {request.Summary}

                      ## Test Id:
                      {request.GetOrGenerateTestId()}

                      ## Title:
                      {request.Title}

                      ## Environment:
                      {environmentContext}

                      ## Purpose - Scope - Acceptance Criteria:
                      {request.AcceptanceCriteria}

                      ## Precondition
                      {request.Precondition}

                      ## Steps:
                      {request.Content}
                      """;
        return prompt;
    }

    private async Task<IMcpClient> GetMcpClient(string outputDir, CancellationToken cancellation)
    {
        var mcpClient = await McpClientFactory.CreateAsync(
            new StdioClientTransport(new StdioClientTransportOptions
            {
                Command = "npx",
                Arguments = ["@playwright/mcp@latest", "--isolated", $"--output-dir={outputDir}"],
                Name = $"Playwright Mcp {Guid.NewGuid()}",
            }), cancellationToken: cancellation);

        return mcpClient;
    }
}