namespace Bravo.AutomationAgent.Models.Configuration;

public class ArtifactSettings
{
    public const string SectionName = "ArtifactSettings";
    
    public string ResultPath { get; set; } = "AgentResult";
}

public class EnvironmentSettings
{
    public const string SectionName = "Environments";
    
    public Dictionary<string, EnvironmentConfig> Environments { get; set; } = new();
}

public class EnvironmentConfig
{
    public string BaseUrl { get; set; } = null!;
    public Dictionary<string, string> Credentials { get; set; } = new();
    public Dictionary<string, string> AdditionalSettings { get; set; } = new();
}
