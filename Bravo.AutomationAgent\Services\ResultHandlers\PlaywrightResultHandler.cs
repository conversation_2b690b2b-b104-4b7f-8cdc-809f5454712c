using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Models.Responses;
using System.Text.Json;
using System.Text.RegularExpressions;
using Bravo.AutomationAgent.Services.Artifacts;

namespace Bravo.AutomationAgent.Services.ResultHandlers;

public class PlaywrightResultHandler(ILogger<PlaywrightResultHandler> logger) : IPlaywrightResultHandler
{
    public Task<TestExecutionResult> HandleMCPResultAsync(dynamic mcpResult, TestExecutionRequest request, IArtifactManager artifactManager)
    {
        logger.LogInformation("Processing MCP result for test: {TestId}", request.GetOrGenerateTestId());
        logger.LogInformation("Processing MCP execution results");

        var testSteps = ParseMCPOutput(mcpResult.Output, logger);
        var overallStatus = mcpResult.Success && DetermineOverallStatus(testSteps) == "Passed" ? "Passed" : "Failed";
        var totalDuration = (int)mcpResult.Duration.TotalMilliseconds;

        var result = new TestExecutionResult
        {
            TestName = request.Title,
            Status = overallStatus,
            Duration = totalDuration,
            Environment = request.Environment,
            TestId = request.GetOrGenerateTestId(),
            Precondition = request.Precondition,
            Steps = testSteps,
            ArtifactPath = artifactManager.GetArtifactPath(request.Environment)
        };

        // Collect artifact files from MCP output
        result.ScreenshotFiles = ExtractScreenshotFiles(mcpResult.Output);

        if (request.EnableVideo)
        {
            result.VideoFile = "video.mp4";
        }

        if (request.EnableTracing)
        {
            result.TraceFile = "trace.zip";
        }

        return Task.FromResult(result);
    }

    private List<TestStep> ParseMCPOutput(string output, ILogger logger)
    {
        var steps = new List<TestStep>();

        if (string.IsNullOrEmpty(output))
        {
            logger.LogWarning("MCP output is empty");
            return steps;
        }

        try
        {
            // Try to parse as JSON first
            if (output.TrimStart().StartsWith("{") || output.TrimStart().StartsWith("["))
            {
                return ParseJsonMCPOutput(output, logger);
            }

            // Parse as plain text output
            return ParseTextMCPOutput(output, logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to parse MCP output");
            logger.LogError("Failed to parse MCP output: {Error}", ex.Message);

            // Return a single failed step
            return [new TestStep
            {
                Step = "Parse MCP output",
                Status = "Failed",
                Duration = 0,
                Error = $"Failed to parse MCP output: {ex.Message}"
            }];
        }
    }

    private List<TestStep> ParseJsonMCPOutput(string output, ILogger logger)
    {
        try
        {
            var jsonDoc = JsonDocument.Parse(output);
            var steps = new List<TestStep>();

            // Handle different JSON structures that MCP might return
            if (jsonDoc.RootElement.TryGetProperty("steps", out var stepsElement))
            {
                foreach (var stepElement in stepsElement.EnumerateArray())
                {
                    var step = ParseJsonStep(stepElement);
                    if (step != null)
                    {
                        steps.Add(step);
                        logger.LogInformation("Step: {Step}, Status: {Status}, Duration: {Duration}, Error: {Error}",
                            step.Step, step.Status, step.Duration, step.Error);
                    }
                }
            }
            else
            {
                // Single step or different format
                var step = ParseJsonStep(jsonDoc.RootElement);
                if (step != null)
                {
                    steps.Add(step);
                    logger.LogInformation("Step: {Step}, Status: {Status}, Duration: {Duration}, Error: {Error}",
                        step.Step, step.Status, step.Duration, step.Error);
                }
            }

            return steps;
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to parse JSON MCP output");
            return ParseTextMCPOutput(output, logger);
        }
    }

    private TestStep? ParseJsonStep(JsonElement element)
    {
        try
        {
            var step = element.GetProperty("step").GetString() ?? "Unknown step";
            var status = element.TryGetProperty("status", out var statusProp) ? statusProp.GetString() ?? "Passed" : "Passed";
            var duration = element.TryGetProperty("duration", out var durationProp) ? durationProp.GetInt32() : 0;
            var error = element.TryGetProperty("error", out var errorProp) ? errorProp.GetString() : null;
            var screenshot = element.TryGetProperty("screenshot", out var screenshotProp) ? screenshotProp.GetString() : null;

            return new TestStep
            {
                Step = step,
                Status = status,
                Duration = duration,
                Error = error,
                Screenshot = screenshot
            };
        }
        catch
        {
            return null;
        }
    }

    private List<TestStep> ParseTextMCPOutput(string output, ILogger logger)
    {
        var steps = new List<TestStep>();
        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            if (string.IsNullOrEmpty(trimmedLine)) continue;

            // Look for step patterns in the output
            if (IsStepLine(trimmedLine))
            {
                var step = ParseStepLine(trimmedLine);
                if (step != null)
                {
                    steps.Add(step);
                    logger.LogInformation("Step: {Step}, Status: {Status}, Duration: {Duration}, Error: {Error}",
                        step.Step, step.Status, step.Duration, step.Error);
                }
            }
        }

        // If no steps found, create a single step from the entire output
        if (!steps.Any())
        {
            var status = output.Contains("error", StringComparison.OrdinalIgnoreCase) ||
                        output.Contains("failed", StringComparison.OrdinalIgnoreCase) ? "Failed" : "Passed";

            steps.Add(new TestStep
            {
                Step = "Execute test automation",
                Status = status,
                Duration = 1000,
                Error = status == "Failed" ? output : null
            });
        }

        return steps;
    }

    private bool IsStepLine(string line)
    {
        // Look for common step patterns
        var stepPatterns = new[]
        {
            @"^\d+\.\s+",           // "1. Step description"
            @"^Step\s+\d+:",        // "Step 1: Description"
            @"^-\s+",               // "- Step description"
            @"^\*\s+",              // "* Step description"
            @"^Navigate\s+",        // "Navigate to..."
            @"^Click\s+",           // "Click element..."
            @"^Fill\s+",            // "Fill input..."
            @"^Verify\s+",          // "Verify..."
            @"^Assert\s+",          // "Assert..."
        };

        return stepPatterns.Any(pattern => Regex.IsMatch(line, pattern, RegexOptions.IgnoreCase));
    }

    private TestStep? ParseStepLine(string line)
    {
        try
        {
            // Extract step description
            var stepDescription = ExtractStepDescription(line);

            // Determine status from line content
            var status = DetermineStepStatus(line);

            // Extract duration if present
            var duration = ExtractDuration(line);

            // Extract error if present
            var error = ExtractError(line);

            // Extract screenshot if present
            var screenshot = ExtractScreenshot(line);

            return new TestStep
            {
                Step = stepDescription,
                Status = status,
                Duration = duration,
                Error = error,
                Screenshot = screenshot
            };
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to parse step line: {Line}", line);
            return null;
        }
    }

    private string ExtractStepDescription(string line)
    {
        // Remove step numbering and common prefixes
        var cleaned = Regex.Replace(line, @"^\d+\.\s*", "");
        cleaned = Regex.Replace(cleaned, @"^Step\s+\d+:\s*", "", RegexOptions.IgnoreCase);
        cleaned = Regex.Replace(cleaned, @"^[-*]\s*", "");

        // Remove status indicators
        cleaned = Regex.Replace(cleaned, @"\s*\[(PASSED|FAILED|PASS|FAIL)\].*$", "", RegexOptions.IgnoreCase);
        cleaned = Regex.Replace(cleaned, @"\s*✅.*$", "");
        cleaned = Regex.Replace(cleaned, @"\s*❌.*$", "");

        return cleaned.Trim();
    }

    private string DetermineStepStatus(string line)
    {
        var lowerLine = line.ToLowerInvariant();

        if (lowerLine.Contains("failed") || lowerLine.Contains("fail") ||
            lowerLine.Contains("error") || lowerLine.Contains("❌"))
        {
            return "Failed";
        }

        return "Passed";
    }

    private int ExtractDuration(string line)
    {
        // Look for duration patterns like "1500ms", "2.5s", etc.
        var durationMatch = Regex.Match(line, @"(\d+(?:\.\d+)?)\s*(ms|milliseconds?|s|seconds?)", RegexOptions.IgnoreCase);

        if (durationMatch.Success)
        {
            if (double.TryParse(durationMatch.Groups[1].Value, out var value))
            {
                var unit = durationMatch.Groups[2].Value.ToLowerInvariant();
                return unit.StartsWith("s") ? (int)(value * 1000) : (int)value;
            }
        }

        // Default duration based on step type
        return EstimateStepDuration(line);
    }

    private string? ExtractError(string line)
    {
        if (DetermineStepStatus(line) == "Failed")
        {
            // Try to extract error message after common patterns
            var errorPatterns = new[]
            {
                @"Error:\s*(.+)$",
                @"Failed:\s*(.+)$",
                @"Exception:\s*(.+)$",
                @"❌\s*(.+)$"
            };

            foreach (var pattern in errorPatterns)
            {
                var match = Regex.Match(line, pattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value.Trim();
                }
            }

            // If no specific error pattern, return the whole line as error
            return line;
        }

        return null;
    }

    private string? ExtractScreenshot(string line)
    {
        // Look for screenshot file references
        var screenshotMatch = Regex.Match(line, @"screenshot[:\-\s]*([^\s]+\.png)", RegexOptions.IgnoreCase);
        return screenshotMatch.Success ? screenshotMatch.Groups[1].Value : null;
    }

    private List<string> ExtractScreenshotFiles(string output)
    {
        var screenshots = new List<string>();
        var screenshotMatches = Regex.Matches(output, @"([^\s]+\.png)", RegexOptions.IgnoreCase);

        foreach (Match match in screenshotMatches)
        {
            var filename = match.Groups[1].Value;
            if (filename.Contains("screenshot", StringComparison.OrdinalIgnoreCase))
            {
                screenshots.Add(Path.GetFileName(filename));
            }
        }

        return screenshots.Distinct().ToList();
    }

    private int EstimateStepDuration(string stepDescription)
    {
        return stepDescription.ToLowerInvariant() switch
        {
            var s when s.Contains("navigate") => 2000,
            var s when s.Contains("click") => 500,
            var s when s.Contains("fill") || s.Contains("type") => 300,
            var s when s.Contains("wait") => 1000,
            var s when s.Contains("screenshot") => 200,
            var s when s.Contains("verify") || s.Contains("assert") => 100,
            _ => 500
        };
    }

    private string DetermineOverallStatus(List<TestStep> steps)
    {
        return steps.Any(s => s.Status == "Failed") ? "Failed" : "Passed";
    }

    private int CalculateTotalDuration(List<TestStep> steps)
    {
        return steps.Sum(s => s.Duration);
    }
}
