using Bravo.AutomationAgent.Models.Requests;
using Bravo.AutomationAgent.Services.Automation;
using Microsoft.AspNetCore.Mvc;

namespace Bravo.AutomationAgent.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PlaywrightController(
    ITestExecutionEngine testExecutionEngine,
    ILogger<PlaywrightController> logger) : ControllerBase
{
    /// <summary>
    /// Execute a test case using the automation framework
    /// </summary>
    /// <param name="request">Test execution request with title, content, environment, etc.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Structured test execution result</returns>
    [HttpPost("execute")]
    public async Task<IActionResult> ExecuteTest([FromBody] TestExecutionRequest request,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            logger.LogWarning("Invalid test execution request received");
            return BadRequest(ModelState);
        }

        if (string.IsNullOrWhiteSpace(request.Title))
        {
            logger.LogWarning("Test title is required but was empty");
            return BadRequest(new { error = "Test title is required." });
        }

        if (string.IsNullOrWhiteSpace(request.Content))
        {
            logger.LogWarning("Test content is required but was empty");
            return BadRequest(new { error = "Test content is required." });
        }

        try
        {
            logger.LogInformation("Executing test: {Title} in environment: {Environment}",
                request.Title, request.Environment);

            var response = await testExecutionEngine.ExecuteTestAsync(request, cancellationToken);

            return Ok(response);
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("Test execution was cancelled for test: {Title}", request.Title);
            return StatusCode(408, new { error = "Test execution was cancelled due to timeout." });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error executing test: {Title}", request.Title);

            return StatusCode(500, new
            {
                error = ex.Message,
                testName = request.Title,
                status = "Failed",
                environment = request.Environment,
                testId = request.GetOrGenerateTestId()
            });
        }
    }
}