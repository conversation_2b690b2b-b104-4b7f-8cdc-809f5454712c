using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Bravo.AutomationAgent.Models.Requests;

public partial class TestExecutionRequest
{
    [JsonPropertyName("summary")] public string? Summary { get; set; }
    [JsonPropertyName("testId")] public string? TestId { get; set; }
    [JsonPropertyName("title")] public string? Title { get; set; }

    /// <summary>
    /// This is the test scope.
    /// </summary>
    [JsonPropertyName("ac")]
    public string? AcceptanceCriteria { get; set; }

    [JsonPropertyName("precondition")] public string? Precondition { get; set; }

    [Required]
    [JsonPropertyName("content")]
    public string Content { get; set; } = null!;

    [Required]
    [JsonPropertyName("environment")]
    public string Environment { get; set; } = "test";
    
    [JsonPropertyName("enableScreenshots")]
    public bool EnableScreenshots { get; set; } = true;

    [JsonPropertyName("enableTracing")] public bool EnableTracing { get; set; } = false;

    [JsonPropertyName("enableVideo")] public bool EnableVideo { get; set; } = false;

    public string GetOrGenerateTestId()
    {
        if (!string.IsNullOrEmpty(TestId))
            return TestId;

        // Generate from title: lowercase, replace spaces with -, remove special characters
        var generated = Title?.ToLowerInvariant();
        if (generated == null) return string.IsNullOrEmpty(generated) ? Guid.NewGuid().ToString("N")[..8] : generated;
        generated = NormalString().Replace(generated, "");
        generated = Hyphen().Replace(generated, "-");
        generated = generated.Trim('-');

        return string.IsNullOrEmpty(generated) ? Guid.NewGuid().ToString("N")[..8] : generated;
    }

    [GeneratedRegex(@"[^a-z0-9\s-]")]
    private static partial Regex NormalString();

    [GeneratedRegex(@"\s+")]
    private static partial Regex Hyphen();
}