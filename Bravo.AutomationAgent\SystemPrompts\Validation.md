﻿## Validation and Verification Rules

### Assertion Types

- **Soft Assertion** (e.g., steps containing the word "Check"):
  - Continue execution after failure
  - Mark the step as failed but proceed
  - Collect all soft assertion results for reporting

- **Hard Assertion / Checkpoint**:
  - Stop test execution immediately on failure
  - Used for critical steps, workflow blockers, rule violations, or when explicitly stated as "Verify" / "Ensure"

### Assertion Timing

- **Validation steps must be fast** — avoid long waits or polling
- Do **not** retry or wait repeatedly unless the user prompt explicitly requests it (e.g., “wait until” or “retry check”)
- Assume the current page state is final unless instructed otherwise

### Test Result Logic

- Final test result is `"Failed"` if **any step fails**, regardless of soft/hard assertion
- All failed steps must include error metadata

### On Any Assertion Failure

- Capture:
  - Actual vs Expected values
  - Selector or logical condition that failed
  - Page state (e.g., current URL, title)

- Screenshot:
  - Always capture full-page screenshot on failure
  - Additionally capture **element-only screenshot** when possible
- Attach trace and video if enabled and failure occurred  
