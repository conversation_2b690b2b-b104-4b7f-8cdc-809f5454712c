using Bravo.AutomationAgent.Infrastructure;
using Bravo.AutomationAgent.Models.Configuration;
using Bravo.AutomationAgent.Services.Artifacts;
using Bravo.AutomationAgent.Services.Automation;
using Bravo.AutomationAgent.Services.Configuration;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
//
// builder.Host.UseSerilog();
// Log.Logger = new LoggerConfiguration()
//     .ReadFrom.Configuration(builder.Configuration)
//     .Enrich.FromLogContext()
//     .WriteTo.Console()
//     .CreateLogger();
// builder.Host.UseSerilog();
// Add configuration options
builder.Services.Configure<ArtifactSettings>(
    builder.Configuration.GetSection(ArtifactSettings.SectionName));

// Add services to the container.
// builder.Services.AddOpenApi(); // Removed duplicate OpenAPI registration
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Bravo Automation API", Version = "v1" });
    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Bravo.AutomationAgent.xml"), true);
});

// Register automation services
builder.Services.AddScoped<ITestExecutionEngine, TestExecutionEngine>();

// Register configuration services
builder.Services.AddScoped<ISystemPromptService, SystemPromptService>();
builder.Services.AddScoped<IChatClientFactory, ChatClientFactory>();
builder.Services.AddScoped<IEnvironmentService, EnvironmentService>();

// Register artifact management
builder.Services.AddScoped<IArtifactManager, ArtifactManager>();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod();
    });
});

var app = builder.Build();

app.UseHttpsRedirection();
app.UseCors();
app.MapControllers();
await app.RunAsync();
// Ensure to flush and close loggers on shutdown
await Log.CloseAndFlushAsync();
