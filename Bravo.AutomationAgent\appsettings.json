{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Chat": {"Provider": "Azure", "Model": "gpt-4o"}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "Model": "gpt-4o-mini"}, "Azure": {"ApiKey": "CclvK1We0cAXoyS3F3zkmYE985vYj3na4qmrWsY4YIrwSM1j2Q87JQQJ99BEACfhMk5XJ3w3AAABACOG6lKO", "Endpoint": "https://ai-up-bravo-dev.openai.azure.com/", "Model": "gpt-4o"}, "Gemini": {"ApiKey": "AIzaSyDvxFESliCpQtyHWwLyQc0g-hGYP9wPPL8", "Model": "gemini-2.0-flash-thinking-exp-01-21"}, "ArtifactSettings": {"ResultPath": "AgentResult"}, "PlaywrightMCP": {"BrowserType": "chromium", "Headless": false, "EnableVideo": true, "EnableTracing": true, "EnableScreenshots": true, "ViewportWidth": 1920, "ViewportHeight": 1080, "Timeout": 30000}, "Environments": {"test": {"baseUrl": "https://test.example.com", "credentials": {"username": "testuser", "password": "testpass"}}, "staging": {"baseUrl": "https://staging.example.com", "credentials": {"username": "staginguser", "password": "stagingpass"}}, "prod": {"baseUrl": "https://example.com", "credentials": {"username": "produser", "password": "prodpass"}}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}]}}